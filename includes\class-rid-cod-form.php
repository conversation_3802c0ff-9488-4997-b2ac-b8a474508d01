<?php
/**
 * Form display and processing class
 */
class RID_COD_Form {
    
    /**
     * Constructor
     */
    public function __construct() {
        // Add form to product page
        add_action('woocommerce_single_product_summary', array($this, 'display_form'), 30);
        
        // Remove Add to Cart button
        add_action('init', array($this, 'remove_default_buttons'));
    }
    
    /**
     * Remove default WooCommerce buttons and quantity elements
     */
    public function remove_default_buttons() {
        // Remove add to cart button
        remove_action('woocommerce_single_product_summary', 'woocommerce_template_single_add_to_cart', 30);
        
        // Remove quantity elements by adding CSS to hide them
        add_action('wp_head', function() {
            echo '<style>
                /* Hide default Add to Cart, quantity, etc. */
                .woocommerce div.product form.cart div.quantity,
                .woocommerce div.product form.cart .button:not(#rid-cod-submit-btn), /* Exclude our button */
                .woocommerce div.product .cart:not(#rid-cod-form), /* Exclude our form */
                .woocommerce-variation-add-to-cart,
                .single_variation_wrap .variations_button,
                .woocommerce div.product .single_add_to_cart_button,
                .woocommerce div.product p.cart,
                /* --- Aggressively hide default variations form/table --- */
                form.variations_form.cart .variations {
                    display: none !important;
                }
            </style>';
        });
    }
    
    /**
     * Display the checkout form
     */
    public function display_form() {
        global $product;
        
        if (!$product) {
            return;
        }
        
        $this->render_form($product->get_id());
    }
    
    /**
     * Shortcode callback
     */
    public function shortcode_callback($atts) {
        $atts = shortcode_atts(array(
            'product_id' => 0, // Changed 'id' to 'product_id'
        ), $atts, 'rid_cod_form');
        
        if (empty($atts['product_id'])) { // Check for 'product_id'
            return '<p>' . __('Please specify a product ID', 'rid-cod') . '</p>';
        }
        
        ob_start();
        $this->render_form($atts['product_id']); // Use 'product_id'
        return ob_get_clean();
    }
    
    /**
     * Render the checkout form
     */
    public function render_form($product_id) {
        $product = wc_get_product($product_id);
        
        if (!$product) {
            return;
        }
        
        $product_type = $product->get_type();
        $product_price = $product->get_price();
        
        // Get Algeria states
        require_once RID_COD_PLUGIN_DIR . 'includes/algeria-states-cities.php';
        $states = rid_cod_get_algeria_states();
        
        // Get shipping zones and methods defined in WooCommerce
        $wc_shipping_methods = $this->get_wc_shipping_methods();

        // Get default per-state costs from settings
        $default_state_costs = $this->get_default_state_costs();

        // Get delivery type settings
        $enable_delivery_type = get_option('rid_cod_enable_delivery_type', 'no') === 'yes';
        $delivery_type_home_label = get_option('rid_cod_delivery_type_home_label', __('توصيل للمنزل', 'rid-cod'));
        $delivery_type_desk_label = get_option('rid_cod_delivery_type_desk_label', __('توصيل للمكتب', 'rid-cod'));

        // Get WhatsApp button settings
        $enable_whatsapp_button = get_option('rid_cod_enable_whatsapp_button', 'yes') === 'yes';
        $whatsapp_number = get_option('rid_cod_whatsapp_number', '');
        
        // Get prevent autocomplete setting
        $prevent_autocomplete = get_option('rid_cod_prevent_autocomplete', 'no') === 'yes';
        $autocomplete_attr = $prevent_autocomplete ? ' autocomplete="off"' : '';

        // Start output buffering
        ob_start();
        ?>
        <div id="rid-cod-checkout">
            <div class="rid-cod-title">
                <h3><?php echo esc_html__('للطلب يرجى ملئ الإستمارة أسفله', 'rid-cod'); ?> <span class="form-title-icon">👇</span></h3>
            </div>
            
            <form id="rid-cod-form" class="checkout">
                <input type="hidden" name="product_id" value="<?php echo esc_attr($product_id); ?>">
                <input type="hidden" name="nonce" value="<?php echo wp_create_nonce('rid_cod_form_nonce'); ?>">
                <input type="hidden" name="quantity" id="rid-cod-quantity" value="1">
                <input type="hidden" name="shipping_method" id="rid-cod-shipping-method" value="">
                <input type="hidden" name="shipping_cost" id="rid-cod-shipping-cost" value="0">
                
                <?php // Removed orphaned else/endif block for simple products, as it was moved with the main 'if variable' block ?>
                
                <div class="rid-cod-customer-info">
                    <div class="rid-cod-field-group rid-cod-field-with-icon">
                        <?php /* <label for="rid-cod-full-name"><?php echo esc_html(get_option('rid_cod_field_name', __('الاسم الكامل', 'rid-cod'))); ?></label> */ ?>
                        <span class="rid-input-icon rid-icon-user"></span>
                        <input type="text" id="rid-cod-full-name" name="full_name" placeholder="<?php echo esc_attr(__('الاسم الكامل', 'rid-cod')); ?>" required<?php echo $autocomplete_attr; ?>>
                    </div>
                    
                    <div class="rid-cod-field-group rid-cod-field-with-icon">
                        <?php /* <label for="rid-cod-phone"><?php echo esc_html(get_option('rid_cod_field_phone', __('رقم الهاتف', 'rid-cod'))); ?></label> */ ?>
                        <span class="rid-input-icon rid-icon-phone"></span>
                        <input type="tel" id="rid-cod-phone" name="phone" placeholder="<?php echo esc_attr(__('رقم الهاتف', 'rid-cod')); ?>" required<?php echo $autocomplete_attr; ?>>
                    </div>
                    
                    <?php if (!empty($states)) : ?>
                        <div class="rid-cod-field-group rid-cod-field-with-icon">
                            <?php /* <label for="rid-cod-state"><?php echo esc_html(get_option('rid_cod_field_state', __('الولاية', 'rid-cod'))); ?></label> */ ?>
                            <span class="rid-input-icon rid-icon-state"></span>
                            <select id="rid-cod-state" name="state" required>
                                <option value="" disabled selected><?php echo esc_html(__('الولاية', 'rid-cod')); ?></option>
                                <?php foreach ($states as $state_code => $state_name) : ?>
                                    <?php $display_text = sprintf('%s - %s', $state_code, $state_name); // Combine code and name ?>
                                    <option value="<?php echo esc_attr($state_code); ?>" data-state="<?php echo esc_attr($state_name); ?>"><?php echo esc_html($display_text); ?></option> <?php // Display combined text ?>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="rid-cod-field-group rid-cod-field-with-icon">
                            <?php /* <label for="rid-cod-city"><?php echo esc_html(get_option('rid_cod_field_city', __('البلدية', 'rid-cod'))); ?></label> */ ?>
                            <span class="rid-input-icon rid-icon-city"></span>
                            <select id="rid-cod-city" name="city" required disabled>
                                <option value="" disabled selected><?php echo esc_html(__('البلدية', 'rid-cod')); ?></option>
                            </select>
                        </div>
                    <?php else : ?>
                        <div class="rid-cod-field-group rid-cod-field-with-icon">
                            <?php /* <label for="rid-cod-state-text"><?php echo esc_html(get_option('rid_cod_field_state', __('الولاية', 'rid-cod'))); ?></label> */ ?>
                            <span class="rid-input-icon rid-icon-state"></span>
                            <input type="text" id="rid-cod-state-text" name="state" placeholder="<?php echo esc_attr(__('الولاية', 'rid-cod')); ?>" required<?php echo $autocomplete_attr; ?>>
                        </div>
                        
                        <div class="rid-cod-field-group rid-cod-field-with-icon">
                            <?php /* <label for="rid-cod-city-text"><?php echo esc_html(get_option('rid_cod_field_city', __('البلدية', 'rid-cod'))); ?></label> */ ?>
                            <span class="rid-input-icon rid-icon-city"></span>
                            <input type="text" id="rid-cod-city-text" name="city" placeholder="<?php echo esc_attr(__('البلدية', 'rid-cod')); ?>" required<?php echo $autocomplete_attr; ?>>
                        </div>
                    <?php endif; ?>

                    <?php // Add Delivery Type options if enabled ?>
                    <?php if ($enable_delivery_type) : ?>
                    <div class="rid-cod-field-group rid-cod-delivery-type">
                        <label><?php esc_html_e('نوع التوصيل:', 'rid-cod'); ?></label>
                        <div class="rid-delivery-options">
                            <label>
                                <input type="radio" name="delivery_type" value="home" checked>
                                <?php echo esc_html($delivery_type_home_label); ?>
                            </label>
                            <label>
                                <input type="radio" name="delivery_type" value="desk">
                                <?php echo esc_html($delivery_type_desk_label); ?>
                            </label>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>

                <?php // --- Variations Section (Using Enhanced WooCommerce Integration) --- ?>
                <?php if ($product_type === 'variable') : ?>
                    <div class="rid-cod-variations rid-cod-field-group">
                        <?php
                        // Get the attributes and available variations for JS
                        $attributes = $product->get_variation_attributes();
                        $available_variations = $product->get_available_variations();
                        
                        // Make sure we have variations data
                        if (empty($available_variations)) {
                            echo '<p class="error">هذا المنتج ليس لديه خيارات متاحة حاليًا.</p>';
                        } else {
                            // Encode the variations data for JavaScript
                            $variations_json = wp_json_encode($available_variations);
                            $variations_attr = function_exists('wc_esc_json') ? wc_esc_json($variations_json) : _wp_specialchars($variations_json, ENT_QUOTES, 'UTF-8', true);
                            
                            // Add required product data attributes for proper WooCommerce integration
                            $product_data_attrs = array(
                                'data-product_id' => $product->get_id(),
                                'data-product_variations' => $variations_attr
                            );
                            
                            // Build a variations form that's compatible with WooCommerce
                            echo '<div class="variations_form cart"';
                            foreach ($product_data_attrs as $key => $value) {
                                echo ' ' . esc_attr($key) . '="' . esc_attr($value) . '"';
                            }
                            echo '>';
                            
                            // Add hidden input for WooCommerce compatibility
                            echo '<input type="hidden" name="product_id" value="' . esc_attr($product->get_id()) . '" />';
                            echo '<input type="hidden" class="variation_id" name="variation_id" value="" />';
                            
                            // Output the variations table
                            echo '<div class="variations">';
                            
                            // Output each attribute select box
                            foreach ($attributes as $attribute_name => $options) {
                                $attribute = sanitize_title($attribute_name);
                                $attribute_label = wc_attribute_label($attribute_name);
                                
                                echo '<div class="rid-cod-attribute-row variation-row">';
                                echo '<label for="' . esc_attr($attribute) . '">' . esc_html($attribute_label) . ':</label>';
                                
                                // Important: Use attribute_ prefix for WooCommerce compatibility
                                $select_name = 'attribute_' . $attribute;
                                
                                // Output standard dropdown with options - WooCommerce compatible naming
                                echo '<select id="' . esc_attr($attribute) . '" name="' . esc_attr($select_name) . '" class="rid-cod-variation-select"';
                                // Add data attribute to help variation script
                                echo ' data-attribute_name="' . esc_attr($select_name) . '">';
                                echo '<option value="">' . esc_html__('اختر', 'rid-cod') . ' ' . esc_html($attribute_label) . '</option>';
                                
                                // Add each option to the dropdown
                                foreach ($options as $option) {
                                    echo '<option value="' . esc_attr($option) . '">' . esc_html($option) . '</option>';
                                }
                                
                                echo '</select>';
                                echo '</div>';
                            }
                            
                            echo '</div>'; // close .variations
                            
                            // Reset variations link - standard WooCommerce markup
                            echo '<a class="reset_variations" href="#">' . esc_html__('Clear', 'woocommerce') . '</a>';
                            
                            // Standard WooCommerce single variation display
                            echo '<div class="single_variation_wrap">';
                            echo '<div class="woocommerce-variation single_variation"></div>';
                            echo '</div>'; // close .single_variation_wrap
                            
                            echo '</div>'; // close .variations_form
                            
                            // Container for our custom variation details
                            echo '<div id="rid-cod-summary-variation-details" style="margin-top: 10px;"></div>';
                            
                            // Add price display element 
                            echo '<div class="product-price-display">';
                            echo '<span>' . esc_html__('السعر:', 'rid-cod') . ' </span>';
                            echo '<span id="rid-cod-product-price">' . esc_html__('اختر النوع', 'rid-cod') . '</span>';
                            echo '</div>';
                        }
                        ?>
                        
                        <?php // Hidden inputs for variation data ?>
                        <input type="hidden" name="variation_id" id="rid-cod-variation-id" value="">
                        <input type="hidden" name="variation_price" id="rid-cod-variation-price" value="">
                        
                        <?php // Debug panel that can be toggled during testing ?>
                        <div id="rid-cod-variation-debug" style="border: 1px solid #ccc; padding: 10px; margin-top: 15px; display: none;">
                            <h4>معلومات تصحيح الأخطاء</h4>
                            <div class="debug-content"></div>
                        </div>
                        
                        <?php if (!empty($available_variations)) : ?>
                        <script type="text/javascript">
                        /* Embed variation data directly after the HTML elements to ensure proper initialization */
                        var rid_cod_variations = <?php echo $variations_json; ?>;
                        jQuery(document).ready(function($) {
                            console.log('RIDCOD: Initializing variations directly');
                            // Force immediate initialization by triggering the WooCommerce variation form
                            $('.variations_form').trigger('wc_variation_form');
                            // Also trigger our fallback handlers
                            $('.rid-cod-variation-select').first().trigger('change');
                        });
                        </script>
                        <?php endif; ?>
                    </div>
                <?php endif; // End if ($product_type === 'variable') ?>
                <?php // --- End Variations Section --- ?>

                <!-- Quantity and Submit Button Row -->
                <div class="rid-cod-actions-row">
                    <div class="rid-cod-submit">
                        <button type="submit" id="rid-cod-submit-btn"><?php echo esc_html(get_option('rid_cod_button_text', __('انقر هنا لتأكيد الطلب', 'rid-cod'))); ?></button>
                    </div>
                    <div class="rid-cod-quantity">
                        <?php /* <label><?php echo esc_html(get_option('rid_cod_field_quantity', __('الكمية', 'rid-cod'))); ?></label> */ ?>
                        <div class="rid-cod-quantity-selector">
                            <button type="button" id="rid-cod-decrease">-</button>
                            <input type="number" id="rid-cod-quantity-input" value="1" min="1" max="<?php echo esc_attr($product->get_stock_quantity() ? $product->get_stock_quantity() : 99); ?>" readonly>
                            <button type="button" id="rid-cod-increase">+</button>
                        </div>
                    </div>
                </div>
                
                <?php // WhatsApp Button - Conditionally displayed ?>
                <?php if ($enable_whatsapp_button && !empty($whatsapp_number)) : ?>
                    <?php
                        // Prepare WhatsApp message with product name and link
                        $whatsapp_message = sprintf(
                            __('أريد طلب المنتج: %1$s%2$sرابط المنتج: %3$s', 'rid-cod'),
                            $product->get_name(), // %1$s Product Name
                            "\n",                 // %2$s New line
                            $product->get_permalink() // %3$s Product URL
                        );
                        $whatsapp_url = 'https://wa.me/' . esc_attr($whatsapp_number) . '?text=' . urlencode($whatsapp_message);
                    ?>
                    <div class="rid-cod-whatsapp-button">
                        <a href="<?php echo esc_url($whatsapp_url); ?>" target="_blank" id="rid-cod-whatsapp-btn">
                            <span class="rid-icon-whatsapp"></span> <?php echo esc_html__('أنقر هنا للطلب عبر الواتساب', 'rid-cod'); ?>
                        </a>
                    </div>
                <?php endif; ?>
            </form>
            
            <!-- Collapsible Order Summary -->
            <div id="rid-cod-summary-wrapper">
                <div id="rid-cod-summary-header">
                    <h4><span class="rid-icon-cart"></span> <?php echo esc_html(get_option('rid_cod_summary_title', __('ملخص الطلب', 'rid-cod'))); ?></h4>
                    <span id="rid-cod-summary-toggle" class="rid-icon-arrow-down"></span>
                </div>
                <div id="rid-cod-summary-content"> <!-- Removed style="display: none;" -->
                    <table>
                        <tr>
                            <td>
                                <?php echo esc_html($product->get_name()); ?>
                                <div id="rid-cod-summary-variation-details" class="rid-summary-variation-details"></div> <!-- Placeholder for variations -->
                            </td>
                            <td>
                                <span class="rid-summary-quantity">x<span id="rid-cod-product-quantity">1</span></span>
                                <span id="rid-cod-product-price"><?php echo $product_type === 'variable' ? esc_html(__('اختر النوع', 'rid-cod')) : wc_price($product_price); ?></span>
                            </td>
                        </tr>
                        <tr>
                            <td><?php echo esc_html(get_option('rid_cod_shipping_text', __('سعر التوصيل', 'rid-cod'))); ?></td>
                            <td id="rid-cod-shipping-price"><?php echo esc_html(__('إختر الولاية', 'rid-cod')); ?></td>
                        </tr>
                        <?php // Add row for shipping type if enabled ?>
                        <?php if ($enable_delivery_type) : ?>
                        <tr id="rid-cod-summary-shipping-type-row" style="display: none;"> <?php // Initially hidden, shown by JS ?>
                            <td><?php echo esc_html(__('نوع التوصيل', 'rid-cod')); ?></td>
                            <td id="rid-cod-summary-shipping-type"><?php echo esc_html($delivery_type_home_label); // Default to home ?></td>
                        </tr>
                        <?php endif; ?>
                        <tr> <?php // Re-add the opening <tr> tag for the total row ?>
                        </tr>
                        <tr class="rid-cod-total">
                            <td><?php echo esc_html(get_option('rid_cod_total_text', __('السعر الإجمالي', 'rid-cod'))); ?></td>
                            <td id="rid-cod-total-price"><?php echo $product_type === 'variable' ? esc_html(__('اختر النوع', 'rid-cod')) : wc_price($product_price); ?></td>
                        </tr>
                    </table>
                </div>
            </div>
            
            <div id="rid-cod-message"></div>

            <?php // --- Sticky Button HTML (Added Here) ---
            $enable_sticky_button = get_option('rid_cod_enable_sticky_button', 'no') === 'yes';
            if ($enable_sticky_button) :
                $sticky_button_text = get_option('rid_cod_button_text', __('انقر هنا لتأكيد الطلب', 'rid-cod'));
            ?>
            <div id="rid-cod-sticky-button-container" class="rid-cod-sticky-button-container">
                <?php // Use the same ID as the original submit button for style inheritance ?>
                <button type="button" id="rid-cod-submit-btn" class="rid-cod-submit-button"><?php echo esc_html($sticky_button_text); ?></button> <?php // Changed ID to rid-cod-submit-btn ?>
            </div>
            <?php endif; ?>
            <?php // --- End Sticky Button HTML --- ?>

        </div> <?php // End #rid-cod-checkout ?>
        <?php

        // Get shipping methods as JSON for JavaScript
        // Pass data to JavaScript
        $js_data = array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('rid_cod_nonce'), // General nonce for JS actions
            'select_city' => __('اختر البلدية', 'rid-cod'),
            'select_variation' => __('اختر النوع', 'rid-cod'),
            'select_state' => __('اختر الولاية', 'rid-cod'),
            'free_shipping' => __('توصيل مجاني', 'rid-cod'),
            'processing' => __('جاري المعالجة...', 'rid-cod'),
            'error' => __('حدث خطأ. يرجى المحاولة مرة أخرى.', 'rid-cod'),
            'wc_shipping_methods' => $wc_shipping_methods, // Methods from WC Zones
            'default_state_costs' => $default_state_costs, // Default costs {desk: cost, home: cost} from settings
            'cities_by_state' => $this->get_cities_by_state(), // Cities data
            'enable_delivery_type' => $enable_delivery_type, // Boolean: Is delivery type option enabled?
            'shipping_unavailable' => __('الشحن غير متوفر', 'rid-cod'), // Text for when shipping cost is not found for a selected state
            'enable_algerian_phone_validation' => get_option('rid_cod_enable_algerian_phone_validation', 'yes') === 'yes', // Pass validation setting to JS
            'prevent_copy_paste' => get_option('rid_cod_prevent_copy_paste', 'no') === 'yes', // Pass copy-paste prevention setting
            'base_product_price' => $product_price, // Pass base product price for simple products
            'enable_sticky_button' => $enable_sticky_button, // Pass sticky button setting to JS
            // Pass sticky button colors for potential JS use (though CSS vars are primary)
            'sticky_button_bg_color' => get_option('rid_cod_sticky_button_bg_color', '#6a3de8'),
            'sticky_button_text_color' => get_option('rid_cod_sticky_button_text_color', '#ffffff'),
            'sticky_button_border_color' => get_option('rid_cod_sticky_button_border_color', '#6a3de8'),
        );

        echo '<script type="text/javascript">
            /* <![CDATA[ */
            var rid_cod_params = ' . wp_json_encode($js_data) . ';
            /* ]]> */
        </script>';

        echo ob_get_clean();
    }
    
    /**
     * Get shipping methods defined in WooCommerce Shipping Zones
     */
    private function get_wc_shipping_methods() {
        $transient_key = 'rid_cod_wc_shipping_methods_cache';
        $cached_methods = get_transient($transient_key);

        if (false !== $cached_methods) {
            return $cached_methods;
        }

        $shipping_methods = array();
        $zones = WC_Shipping_Zones::get_zones();

        foreach ($zones as $zone_id => $zone) {
            $zone_obj = new WC_Shipping_Zone($zone['id']);
            $methods = $zone_obj->get_shipping_methods(true);

            foreach ($methods as $method) {
                $method_id = $method->id;
                $method_instance_id = $method->instance_id;
                $method_title = $method->get_title();
                $method_cost = 0;

                if ($method_id === 'flat_rate') {
                    $method_cost = $method->get_option('cost');
                } elseif ($method_id === 'free_shipping') {
                    $method_cost = 0;
                }

                if (!isset($shipping_methods[$zone['zone_name']])) {
                    $shipping_methods[$zone['zone_name']] = array();
                }

                $shipping_methods[$zone['zone_name']][] = array(
                    'id' => $method_instance_id,
                    'method_id' => $method_id,
                    'title' => $method_title,
                    'cost' => $method_cost,
                    'locations' => $zone['zone_locations']
                );
            }
        }

        // Cache for 12 hours (or until manually cleared)
        set_transient($transient_key, $shipping_methods, 12 * HOUR_IN_SECONDS);

        return $shipping_methods;
    }
    
    /**
     * Get default shipping costs for each state from plugin settings.
     *
     * @return array Associative array with state_code => cost.
     */
    private function get_default_state_costs() {
        $transient_key = 'rid_cod_default_costs_cache';
        $cached_costs = get_transient($transient_key);

        if (false !== $cached_costs) {
            return $cached_costs;
        }

        $state_costs = array();
        // Ensure states file is loaded if needed
        if (!function_exists('rid_cod_get_algeria_states')) {
             require_once RID_COD_PLUGIN_DIR . 'includes/algeria-states-cities.php';
        }
        $states = function_exists('rid_cod_get_algeria_states') ? rid_cod_get_algeria_states() : array();

        if (!empty($states)) {
            foreach (array_keys($states) as $state_code) {
                $desk_cost = get_option('rid_cod_cost_state_desk_' . $state_code);
                $home_cost = get_option('rid_cod_cost_state_home_' . $state_code);

                $state_costs[$state_code] = [
                    'desk' => ($desk_cost !== '' && is_numeric($desk_cost)) ? floatval($desk_cost) : null,
                    'home' => ($home_cost !== '' && is_numeric($home_cost)) ? floatval($home_cost) : null,
                ];
            }
        }

        // Cache for 12 hours (or until manually cleared)
        set_transient($transient_key, $state_costs, 12 * HOUR_IN_SECONDS);

        return $state_costs;
    }

    
    /**
     * Get cities by state
     */
    private function get_cities_by_state() {
        // Include Algeria states and cities data
        require_once RID_COD_PLUGIN_DIR . 'includes/algeria-states-cities.php';
        
        // Return the cities by state from the Algeria data file
        // Ensure the function exists before calling
        if (function_exists('rid_cod_get_cities_by_state')) {
             return rid_cod_get_cities_by_state();
        }
        return array(); // Return empty array if function doesn't exist
    }
}