/**
 * RIDCOD Variations Handler - Enhanced Integration with WooCommerce
 * This version integrates directly with WooCommerce variation events for better compatibility
 */
jQuery(document).ready(function($) {
    console.log('RIDCOD Variations: Initializing Enhanced WooCommerce Handler');

    // Get variation data from global variable (set in PHP)
    var variationsData = (typeof rid_cod_variations !== 'undefined') ? rid_cod_variations : [];

    // Only proceed if we have variation elements and data
    if ($('.rid-cod-variations').length === 0 || variationsData.length === 0) {
        console.log('RIDCOD Variations: No variations to handle');
        return;
    }

    console.log('RIDCOD Variations: Found ' + variationsData.length + ' variations');

    // Cache DOM elements
    var $form = $('#rid-cod-form');
    var $variationSelects = $('.rid-cod-variations select.rid-cod-variation-select');
    var $variationIdInput = $('#rid-cod-variation-id');
    var $variationPriceInput = $('#rid-cod-variation-price');
    var $productPrice = $('#rid-cod-product-price');
    var $summaryVariationDetails = $('#rid-cod-summary-variation-details');
    var $totalPrice = $('#rid-cod-total-price'); // For updating total if it exists
    var $debugElement = $('#rid-cod-variation-debug .debug-content');
    var $productImage = $('.woocommerce-product-gallery__image img, .wp-post-image').first();
    var originalImageSrc = $productImage.attr('src');

    // Add debug button for testing
    var $debugToggle = $('<button type="button" id="toggle-debug" style="margin-top:10px;">Debug Info</button>');
    $('#rid-cod-variation-debug').before($debugToggle);
    $debugToggle.on('click', function() {
        $('#rid-cod-variation-debug').toggle();
    });

    // Connect to WooCommerce variations container - now a div, not a form
    var $wooVariationsForm = $('.rid-cod-variations .variations_form');
    if ($wooVariationsForm.length > 0) {
        console.log('RIDCOD: WooCommerce variations container found, connecting events');

        // Since it's now a div, we need to manually trigger WooCommerce events
        // We'll handle variation logic ourselves and sync with WooCommerce when needed
    } else {
        console.warn('RIDCOD: No WooCommerce variations container found, using custom handlers');
        // We'll need to rely on our own handlers
        $wooVariationsForm = $('.rid-cod-variations');
    }
    
    // Format price with currency symbol
    function formatPrice(price) {
        return 'دج ' + parseFloat(price).toFixed(2);
    }
    
    // Find matching variation based on selected attributes
    function findMatchingVariation() {
        // Get all selected attributes
        var selectedAttrs = {};
        var allSelected = true;
        
        $variationSelects.each(function() {
            var attrName = $(this).attr('name');
            var attrValue = $(this).val();
            
            if (!attrValue) {
                allSelected = false;
                return false; // Break loop
            }
            
            selectedAttrs[attrName] = attrValue;
        });
        
        // Log for debugging
        $debugElement.html('')
            .append('<p><strong>Selected Attributes:</strong> ' + JSON.stringify(selectedAttrs) + '</p>')
            .append('<p><strong>All Selected:</strong> ' + allSelected + '</p>');
        console.log('RIDCOD: Selected attributes', selectedAttrs, 'All selected:', allSelected);
        
        // Need all attributes selected to find a variation
        if (!allSelected) return null;
        
        // Find matching variation
        for (var i = 0; i < variationsData.length; i++) {
            var variation = variationsData[i];
            var isMatch = true;
            
            // Check if all selected attributes match this variation
            for (var attrName in selectedAttrs) {
                // Get cleaned attribute name (WooCommerce uses attribute_pa_size format)
                var cleanAttrName = attrName.replace('attribute_', '');
                var selectedValue = selectedAttrs[attrName];
                var variationAttr = variation.attributes[attrName] || variation.attributes['attribute_' + cleanAttrName];
                
                // If variation has a specific value for this attribute and it doesn't match selection
                if (variationAttr && variationAttr !== '' && variationAttr !== selectedValue) {
                    isMatch = false;
                    break;
                }
            }
            
            if (isMatch) {
                // Debug
                $debugElement.append('<p><strong>Found matching variation:</strong> ' + variation.variation_id + '</p>');
                console.log('RIDCOD: Found variation', variation);
                return variation;
            }
        }
        
        // No match found
        $debugElement.append('<p><strong>No matching variation found!</strong></p>');
        console.log('RIDCOD: No matching variation found');
        return null;
    }
    
    // New function for handling WooCommerce variation objects directly
    function updateVariationUI(variation) {
        if (!variation || !variation.variation_id) {
            // Reset form when no variation is selected
            updateUI(null);
            return;
        }
        
        console.log('RIDCOD: Updating UI with WooCommerce variation', variation);
        
        // Update hidden inputs
        $variationIdInput.val(variation.variation_id);
        $variationPriceInput.val(variation.display_price);
        
        // Update visible price
        if (variation.price_html) {
            $productPrice.html(variation.price_html);
        } else {
            $productPrice.html(formatPrice(variation.display_price));
        }
        
        // Update variation summary in details section
        var summaryHtml = '';
        for (var attribute in variation.attributes) {
            if (variation.attributes.hasOwnProperty(attribute)) {
                // Get attribute name without prefix
                var attrName = attribute.replace('attribute_', '');
                // Find the select element for this attribute
                var $select = $variationSelects.filter('[name="' + attribute + '"]');
                var label = $select.closest('.variation-row').find('label').text().replace(':', '');
                var selectedText = $select.find('option:selected').text();
                
                summaryHtml += '<span class="variation-detail">' + label + ': ' + selectedText + '</span><br>';
            }
        }
        $summaryVariationDetails.html(summaryHtml);
        
        // Update product image if variation has one
        if (variation.image && variation.image.src) {
            $productImage.attr('src', variation.image.src);
        }
        
        // Call external update functions if they exist
        if (typeof updateTotalPrice === 'function') {
            updateTotalPrice();
        }
        
        // Log for debugging
        $debugElement.html('')
            .append('<p><strong>WooCommerce Variation:</strong></p>')
            .append('<p>ID: ' + variation.variation_id + '</p>')
            .append('<p>Price: ' + variation.display_price + '</p>')
            .append('<p>Attributes: ' + JSON.stringify(variation.attributes) + '</p>');
    }
    
    // Original updateUI function - kept for backward compatibility
    function updateUI(variation) {
        if (!variation) {
            // Reset form when no variation is selected
            $variationIdInput.val('');
            $variationPriceInput.val('');
            $productPrice.html('اختر النوع');
            $summaryVariationDetails.html('');
            
            // Reset image to original
            if (originalImageSrc) {
                $productImage.attr('src', originalImageSrc);
            }
            return;
        }
        
        // Update hidden inputs
        $variationIdInput.val(variation.variation_id);
        $variationPriceInput.val(variation.display_price);
        
        // Update visible price
        if (variation.price_html) {
            $productPrice.html(variation.price_html);
        } else {
            $productPrice.html(formatPrice(variation.display_price));
        }
        
        // Update variation summary in details section
        var summaryHtml = '';
        $variationSelects.each(function() {
            var $select = $(this);
            var label = $select.closest('.variation-row').find('label').text().replace(':', '');
            var selectedText = $select.find('option:selected').text();
            summaryHtml += '<span class="variation-detail">' + label + ': ' + selectedText + '</span><br>';
        });
        $summaryVariationDetails.html(summaryHtml);
        
        // Update product image if variation has one
        if (variation.image && variation.image.src) {
            $productImage.attr('src', variation.image.src);
        }
        
        // Call external update functions if they exist
        if (typeof updateTotalPrice === 'function') {
            updateTotalPrice();
        }
    }
    
    // Since we're not using a nested form, we'll handle variation selection manually
    // and sync with the main form

    // Handle reset button
    $('.reset_variations').on('click', function(e) {
        e.preventDefault();
        console.log('RIDCOD: Reset variations clicked');

        // Reset all variation selects
        $variationSelects.val('').trigger('change');

        // Force UI reset immediately for better UX
        updateUI(null);

        // Clear main form hidden inputs
        $variationIdInput.val('');
        $variationPriceInput.val('');

        // Update WooCommerce hidden input too
        $('.variation_id').val('0');
    });
    
    // Handle variation select changes with enhanced UX
    $variationSelects.on('change', function() {
        console.log('RIDCOD: Variation select changed');

        var $select = $(this);

        // Add visual feedback
        $select.addClass('changing');
        setTimeout(function() {
            $select.removeClass('changing');
        }, 300);

        // Find matching variation
        var variation = findMatchingVariation();
        if (variation) {
            updateUI(variation);

            // Sync with main form hidden inputs
            $variationIdInput.val(variation.variation_id);
            $variationPriceInput.val(variation.display_price);

            // Update WooCommerce hidden input too
            $('.variation_id').val(variation.variation_id);

            // Trigger total price update in main script
            if (typeof updateTotalPrice === 'function') {
                updateTotalPrice();
            }

            // Add subtle success animation to the variations container
            $('.rid-cod-variations').addClass('variation-updated');
            setTimeout(function() {
                $('.rid-cod-variations').removeClass('variation-updated');
            }, 600);

        } else {
            // No variation found, reset
            updateUI(null);
            $variationIdInput.val('');
            $variationPriceInput.val('');
            $('.variation_id').val('0');

            // Trigger total price update in main script
            if (typeof updateTotalPrice === 'function') {
                updateTotalPrice();
            }
        }
    });
    
    // Form validation - prevent submission if no variation selected
    $form.on('submit', function(e) {
        console.log('RIDCOD: Form submitted');
        
        // Only validate if we have variations
        if ($variationSelects.length > 0) {
            // Check if we have a valid variation selection
            if ($variationIdInput.val() === '') {
                // Try multiple approaches to get the variation ID
                
                // 1. Check if WooCommerce has selected a variation
                var $wooVariationInput = $wooVariationsForm.find('input.variation_id');
                if ($wooVariationInput.length && $wooVariationInput.val() !== '' && $wooVariationInput.val() !== '0') {
                    // WooCommerce has a variation selected - use it
                    console.log('RIDCOD: Using WooCommerce selected variation ID: ' + $wooVariationInput.val());
                    
                    // Copy the variation ID to our form
                    $variationIdInput.val($wooVariationInput.val());
                    
                    // Find the full variation data for price
                    var wooVariationId = parseInt($wooVariationInput.val());
                    for (var i = 0; i < variationsData.length; i++) {
                        if (parseInt(variationsData[i].variation_id) === wooVariationId) {
                            $variationPriceInput.val(variationsData[i].display_price);
                            break;
                        }
                    }
                    
                    // Form can now be submitted
                } else {
                    // 2. Try our custom matching logic
                    var variation = findMatchingVariation();
                    
                    // If still no variation, prevent submission
                    if (!variation) {
                        e.preventDefault();
                        
                        // Show error message
                        alert('يرجى اختيار نوع المنتج أولاً');
                        
                        // Highlight selects
                        $variationSelects.addClass('error');
                        
                        // Scroll to variations section
                        $('html, body').animate({
                            scrollTop: $wooVariationsForm.offset().top - 100
                        }, 500);
                        
                        return false;
                    } else {
                        // We found a variation at the last moment, update UI and continue
                        updateUI(variation);
                    }
                }
            }
        }
    });
    
    // Check for pre-selected values on page load
    setTimeout(function() {
        // First check if WooCommerce has already selected a variation
        var $wooVariationInput = $wooVariationsForm.find('input.variation_id');
        if ($wooVariationInput.length && $wooVariationInput.val() !== '' && $wooVariationInput.val() !== '0') {
            // WooCommerce has a variation selected - find and use it
            var wooVariationId = parseInt($wooVariationInput.val());
            for (var i = 0; i < variationsData.length; i++) {
                if (parseInt(variationsData[i].variation_id) === wooVariationId) {
                    updateUI(variationsData[i]);
                    return; // Exit early since we found a variation
                }
            }
        }
        
        // Check if URL contains variation data
        var urlParams = new URLSearchParams(window.location.search);
        if (urlParams.has('variation_id')) {
            var urlVariationId = parseInt(urlParams.get('variation_id'));
            for (var i = 0; i < variationsData.length; i++) {
                if (parseInt(variationsData[i].variation_id) === urlVariationId) {
                    // Pre-select attributes in dropdowns
                    var attrs = variationsData[i].attributes;
                    for (var attr in attrs) {
                        if (attrs.hasOwnProperty(attr) && attrs[attr] !== '') {
                            $variationSelects.filter('[name="' + attr + '"]').val(attrs[attr]);
                        }
                    }
                    // Update UI
                    updateUI(variationsData[i]);
                    return; // Exit early
                }
            }
        }
        
        // Fallback to our custom matching logic
        var variation = findMatchingVariation();
        if (variation) {
            updateUI(variation);
        }
    }, 300); // Increased timeout to allow WooCommerce to initialize
});
